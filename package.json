{"name": "sayari-blog", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build:analyze": "vite build --mode analyze", "perf:test": "node scripts/performance-test.js", "perf:build": "npm run build && npm run preview & sleep 5 && npm run perf:test && pkill -f 'vite preview'"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.4", "typescript": "~5.8.3", "vite": "^7.0.4"}, "dependencies": {"@supabase/supabase-js": "^2.53.0", "dotenv": "^17.2.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.7.1"}}